"""
分布式数据采集、清洗和存储系统

这是一个基于Python 3.11的现代化数据处理平台，支持：
- 分布式爬虫采集
- 实时数据清洗
- 多种存储后端
- 任务调度和监控
"""

__version__ = "0.1.0"
__author__ = "Data Trans Team"
__email__ = "<EMAIL>"

# 导入主要模块（延迟导入以避免依赖问题）
try:
    from . import api, cleaners, config, crawlers, queue, storage
    # executor模块包含Ray依赖，在Windows上可能有问题，所以单独处理
    try:
        from . import executor
    except ImportError as e:
        import warnings
        warnings.warn(f"无法导入executor模块（可能是Ray依赖问题）: {e}")
        executor = None
except ImportError as e:
    import warnings
    warnings.warn(f"导入模块时出现问题: {e}")

__all__ = [
    "crawlers",
    "cleaners",
    "storage",
    "api",
    "config",
    "executor",
    "queue",
]
