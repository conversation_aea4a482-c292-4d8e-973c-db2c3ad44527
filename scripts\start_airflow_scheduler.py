#!/usr/bin/env python3
"""
启动Airflow Scheduler
"""

import os
import sys
import subprocess
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
AIRFLOW_HOME = PROJECT_ROOT / "airflow"

def setup_environment():
    """设置环境变量"""
    os.environ["AIRFLOW_HOME"] = str(AIRFLOW_HOME)
    os.environ["AIRFLOW__CORE__DAGS_FOLDER"] = str(AIRFLOW_HOME / "dags")
    os.environ["AIRFLOW__CORE__BASE_LOG_FOLDER"] = str(AIRFLOW_HOME / "logs")
    os.environ["AIRFLOW__CORE__PLUGINS_FOLDER"] = str(AIRFLOW_HOME / "plugins")
    os.environ["AIRFLOW__CORE__EXECUTOR"] = "SequentialExecutor"
    os.environ["AIRFLOW__DATABASE__SQL_ALCHEMY_CONN"] = "sqlite:////" + str(AIRFLOW_HOME / "airflow.db")
    os.environ["AIRFLOW__CORE__LOAD_EXAMPLES"] = "False"

def start_scheduler():
    """启动Airflow scheduler"""
    setup_environment()
    
    print("启动Airflow Scheduler...")
    print("按 Ctrl+C 停止服务")
    
    try:
        subprocess.run([
            sys.executable, "-m", "airflow", "scheduler"
        ], cwd=PROJECT_ROOT)
    except KeyboardInterrupt:
        print("\nAirflow Scheduler已停止")

if __name__ == "__main__":
    start_scheduler()
