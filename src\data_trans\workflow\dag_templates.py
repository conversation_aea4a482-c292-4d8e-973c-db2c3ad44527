"""
DAG模板 - 预定义的工作流模板
"""

from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from .base import BaseDAG
from .operators import CrawlerOperator, CleanerOperator, StorageOperator, NotificationOperator
from .sensors import DataSourceSensor, QueueSensor, TimeSensor


def create_simple_crawl_dag(
    dag_id: str,
    target_urls: List[str],
    crawler_config: Optional[Dict[str, Any]] = None,
    cleaner_config: Optional[Dict[str, Any]] = None,
    storage_config: Optional[Dict[str, Any]] = None,
    schedule_interval: Optional[str] = None,
    start_date: Optional[datetime] = None,
    **kwargs
) -> BaseDAG:
    """
    创建简单爬取DAG
    
    工作流: 爬取 -> 清洗 -> 存储 -> 通知
    """
    
    # 默认配置
    default_crawler_config = {
        "timeout": 30,
        "max_retries": 3,
        "delay": 1,
        "user_agent": "DataTrans/1.0"
    }
    
    default_cleaner_config = {
        "remove_html": True,
        "normalize_whitespace": True,
        "remove_empty": True
    }
    
    default_storage_config = {
        "type": "mongodb",
        "collection": f"crawl_data_{dag_id}"
    }
    
    # 合并配置
    crawler_config = {**default_crawler_config, **(crawler_config or {})}
    cleaner_config = {**default_cleaner_config, **(cleaner_config or {})}
    storage_config = {**default_storage_config, **(storage_config or {})}
    
    # 创建DAG
    dag = BaseDAG(
        dag_id=dag_id,
        description=f"简单爬取工作流: {dag_id}",
        schedule_interval=schedule_interval,
        start_date=start_date or datetime.now(),
        catchup=False,
        max_active_runs=1,
        **kwargs
    )
    
    # 创建任务
    crawler_task = CrawlerOperator(
        task_id="crawler",
        dag=dag,
        crawler_config=crawler_config,
        target_urls=target_urls,
        output_key="crawl_result"
    )
    
    cleaner_task = CleanerOperator(
        task_id="cleaner",
        dag=dag,
        cleaner_config=cleaner_config,
        input_key="crawl_result",
        output_key="clean_result"
    )
    
    storage_task = StorageOperator(
        task_id="storage",
        dag=dag,
        storage_config=storage_config,
        input_key="clean_result",
        storage_type=storage_config.get("type", "mongodb"),
        collection_name=storage_config.get("collection")
    )
    
    notification_task = NotificationOperator(
        task_id="notification",
        dag=dag,
        notification_config={"channels": ["log"]},
        message_template="简单爬取DAG {dag_id} 执行完成"
    )
    
    # 设置任务依赖
    crawler_task >> cleaner_task >> storage_task >> notification_task
    
    return dag


def create_batch_process_dag(
    dag_id: str,
    url_batches: List[List[str]],
    crawler_config: Optional[Dict[str, Any]] = None,
    cleaner_config: Optional[Dict[str, Any]] = None,
    storage_config: Optional[Dict[str, Any]] = None,
    schedule_interval: Optional[str] = None,
    start_date: Optional[datetime] = None,
    **kwargs
) -> BaseDAG:
    """
    创建批量处理DAG
    
    工作流: 多个并行爬取任务 -> 合并清洗 -> 存储 -> 通知
    """
    
    # 默认配置
    default_crawler_config = {
        "timeout": 30,
        "max_retries": 3,
        "delay": 1,
        "concurrent_requests": 5
    }
    
    default_cleaner_config = {
        "remove_html": True,
        "normalize_whitespace": True,
        "remove_empty": True,
        "batch_size": 100
    }
    
    default_storage_config = {
        "type": "mongodb",
        "collection": f"batch_data_{dag_id}"
    }
    
    # 合并配置
    crawler_config = {**default_crawler_config, **(crawler_config or {})}
    cleaner_config = {**default_cleaner_config, **(cleaner_config or {})}
    storage_config = {**default_storage_config, **(storage_config or {})}
    
    # 创建DAG
    dag = BaseDAG(
        dag_id=dag_id,
        description=f"批量处理工作流: {dag_id}",
        schedule_interval=schedule_interval,
        start_date=start_date or datetime.now(),
        catchup=False,
        max_active_runs=1,
        **kwargs
    )
    
    # 创建多个并行爬取任务
    crawler_tasks = []
    for i, urls in enumerate(url_batches):
        crawler_task = CrawlerOperator(
            task_id=f"crawler_batch_{i}",
            dag=dag,
            crawler_config=crawler_config,
            target_urls=urls,
            output_key=f"crawl_result_batch_{i}"
        )
        crawler_tasks.append(crawler_task)
    
    # 创建合并清洗任务
    cleaner_task = CleanerOperator(
        task_id="batch_cleaner",
        dag=dag,
        cleaner_config=cleaner_config,
        input_key="crawl_result_batch_0",  # 主要输入
        output_key="batch_clean_result"
    )
    
    # 创建存储任务
    storage_task = StorageOperator(
        task_id="batch_storage",
        dag=dag,
        storage_config=storage_config,
        input_key="batch_clean_result",
        storage_type=storage_config.get("type", "mongodb"),
        collection_name=storage_config.get("collection")
    )
    
    # 创建通知任务
    notification_task = NotificationOperator(
        task_id="batch_notification",
        dag=dag,
        notification_config={"channels": ["log"]},
        message_template="批量处理DAG {dag_id} 执行完成，处理了 {batch_count} 个批次"
    )
    
    # 设置任务依赖：所有爬取任务完成后进行清洗
    for crawler_task in crawler_tasks:
        crawler_task >> cleaner_task
    
    cleaner_task >> storage_task >> notification_task
    
    return dag


def create_realtime_dag(
    dag_id: str,
    data_source_config: Dict[str, Any],
    crawler_config: Optional[Dict[str, Any]] = None,
    cleaner_config: Optional[Dict[str, Any]] = None,
    storage_config: Optional[Dict[str, Any]] = None,
    sensor_config: Optional[Dict[str, Any]] = None,
    **kwargs
) -> BaseDAG:
    """
    创建实时处理DAG
    
    工作流: 数据源监控 -> 触发爬取 -> 清洗 -> 存储 -> 通知
    """
    
    # 默认配置
    default_sensor_config = {
        "check_interval": 60,
        "timeout": 3600
    }
    
    default_crawler_config = {
        "timeout": 30,
        "max_retries": 3,
        "delay": 0.5,
        "realtime_mode": True
    }
    
    default_cleaner_config = {
        "remove_html": True,
        "normalize_whitespace": True,
        "remove_empty": True,
        "realtime_processing": True
    }
    
    default_storage_config = {
        "type": "mongodb",
        "collection": f"realtime_data_{dag_id}"
    }
    
    # 合并配置
    sensor_config = {**default_sensor_config, **(sensor_config or {})}
    crawler_config = {**default_crawler_config, **(crawler_config or {})}
    cleaner_config = {**default_cleaner_config, **(cleaner_config or {})}
    storage_config = {**default_storage_config, **(storage_config or {})}
    
    # 创建DAG
    dag = BaseDAG(
        dag_id=dag_id,
        description=f"实时处理工作流: {dag_id}",
        schedule_interval=None,  # 由传感器触发
        start_date=datetime.now(),
        catchup=False,
        max_active_runs=3,  # 允许多个实例并行
        **kwargs
    )
    
    # 创建数据源传感器
    source_type = data_source_config.get("type", "file")
    if source_type == "queue":
        sensor_task = QueueSensor(
            task_id="queue_sensor",
            dag=dag,
            queue_name=data_source_config.get("queue_name", "default"),
            min_messages=data_source_config.get("min_messages", 1),
            check_interval=sensor_config["check_interval"]
        )
    else:
        sensor_task = DataSourceSensor(
            task_id="data_source_sensor",
            dag=dag,
            source_type=source_type,
            source_config=data_source_config,
            check_interval=sensor_config["check_interval"]
        )
    
    # 创建爬取任务
    crawler_task = CrawlerOperator(
        task_id="realtime_crawler",
        dag=dag,
        crawler_config=crawler_config,
        target_urls=data_source_config.get("urls", []),
        output_key="realtime_crawl_result"
    )
    
    # 创建清洗任务
    cleaner_task = CleanerOperator(
        task_id="realtime_cleaner",
        dag=dag,
        cleaner_config=cleaner_config,
        input_key="realtime_crawl_result",
        output_key="realtime_clean_result"
    )
    
    # 创建存储任务
    storage_task = StorageOperator(
        task_id="realtime_storage",
        dag=dag,
        storage_config=storage_config,
        input_key="realtime_clean_result",
        storage_type=storage_config.get("type", "mongodb"),
        collection_name=storage_config.get("collection")
    )
    
    # 创建通知任务
    notification_task = NotificationOperator(
        task_id="realtime_notification",
        dag=dag,
        notification_config={"channels": ["log"]},
        message_template="实时处理DAG {dag_id} 处理完成一批数据"
    )
    
    # 设置任务依赖
    sensor_task >> crawler_task >> cleaner_task >> storage_task >> notification_task
    
    return dag


def create_scheduled_dag(
    dag_id: str,
    schedule_time: str,  # 格式: "HH:MM"
    target_urls: List[str],
    crawler_config: Optional[Dict[str, Any]] = None,
    cleaner_config: Optional[Dict[str, Any]] = None,
    storage_config: Optional[Dict[str, Any]] = None,
    **kwargs
) -> BaseDAG:
    """
    创建定时调度DAG
    
    工作流: 时间传感器 -> 爬取 -> 清洗 -> 存储 -> 通知
    """
    
    # 创建DAG
    dag = BaseDAG(
        dag_id=dag_id,
        description=f"定时调度工作流: {dag_id}",
        schedule_interval=None,  # 由时间传感器控制
        start_date=datetime.now(),
        catchup=False,
        max_active_runs=1,
        **kwargs
    )
    
    # 创建时间传感器
    time_sensor = TimeSensor(
        task_id="time_sensor",
        dag=dag,
        target_time=schedule_time,
        check_interval=60
    )
    
    # 创建爬取任务
    crawler_task = CrawlerOperator(
        task_id="scheduled_crawler",
        dag=dag,
        crawler_config=crawler_config or {},
        target_urls=target_urls,
        output_key="scheduled_crawl_result"
    )
    
    # 创建清洗任务
    cleaner_task = CleanerOperator(
        task_id="scheduled_cleaner",
        dag=dag,
        cleaner_config=cleaner_config or {},
        input_key="scheduled_crawl_result",
        output_key="scheduled_clean_result"
    )
    
    # 创建存储任务
    storage_task = StorageOperator(
        task_id="scheduled_storage",
        dag=dag,
        storage_config=storage_config or {},
        input_key="scheduled_clean_result"
    )
    
    # 创建通知任务
    notification_task = NotificationOperator(
        task_id="scheduled_notification",
        dag=dag,
        notification_config={"channels": ["log"]},
        message_template=f"定时任务 {dag_id} 在 {schedule_time} 执行完成"
    )
    
    # 设置任务依赖
    time_sensor >> crawler_task >> cleaner_task >> storage_task >> notification_task
    
    return dag
