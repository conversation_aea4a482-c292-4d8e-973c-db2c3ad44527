"""
工作流系统测试
"""

import asyncio
import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from data_trans.workflow.base import BaseDAG, BaseOperator, XCom, TaskState
from data_trans.workflow.operators import <PERSON>rawlerOperator, CleanerOperator, StorageOperator
from data_trans.workflow.sensors import DataSourceSensor, QueueSensor, TimeSensor
from data_trans.workflow.scheduler import WorkflowScheduler, DAGRunState
from data_trans.workflow.notifications import NotificationManager
from data_trans.workflow.dag_templates import create_simple_crawl_dag


class MockOperator(BaseOperator):
    """测试用的模拟操作器"""
    
    def __init__(self, task_id: str, dag, return_value=None, should_fail=False, **kwargs):
        super().__init__(task_id, dag, **kwargs)
        self.return_value = return_value
        self.should_fail = should_fail
        self.executed = False
    
    async def execute(self, context):
        self.executed = True
        if self.should_fail:
            raise Exception(f"Mock task {self.task_id} failed")
        return self.return_value


@pytest.mark.asyncio
class TestWorkflowBase:
    """测试工作流基础功能"""
    
    async def test_dag_creation(self):
        """测试DAG创建"""
        dag = BaseDAG(
            dag_id="test_dag",
            description="测试DAG",
            start_date=datetime.now()
        )
        
        assert dag.dag_id == "test_dag"
        assert dag.description == "测试DAG"
        assert len(dag.tasks) == 0
    
    async def test_task_dependencies(self):
        """测试任务依赖关系"""
        dag = BaseDAG(dag_id="test_dag")
        
        task1 = MockOperator("task1", dag, return_value="result1")
        task2 = MockOperator("task2", dag, return_value="result2")
        task3 = MockOperator("task3", dag, return_value="result3")
        
        # 设置依赖关系: task1 -> task2 -> task3
        task1 >> task2 >> task3
        
        assert task2.task_id in task1.downstream_task_ids
        assert task1.task_id in task2.upstream_task_ids
        assert task3.task_id in task2.downstream_task_ids
        assert task2.task_id in task3.upstream_task_ids
    
    async def test_xcom_data_exchange(self):
        """测试XCom数据交换"""
        # 清理XCom数据
        XCom._data.clear()
        
        # 推送数据
        XCom.push("test_key", "test_value", "task1", "test_dag")
        
        # 拉取数据
        value = XCom.pull("test_key", "task1", "test_dag")
        assert value == "test_value"
        
        # 清理DAG数据
        XCom.clear("test_dag")
        value = XCom.pull("test_key", "task1", "test_dag")
        assert value is None
    
    async def test_dag_validation(self):
        """测试DAG验证"""
        dag = BaseDAG(dag_id="test_dag")
        
        task1 = MockOperator("task1", dag)
        task2 = MockOperator("task2", dag)
        task3 = MockOperator("task3", dag)
        
        # 正常依赖关系
        task1 >> task2 >> task3
        assert dag.validate() == True
        
        # 创建循环依赖
        task3.set_downstream(task1)
        assert dag.validate() == False
    
    async def test_dag_execution(self):
        """测试DAG执行"""
        dag = BaseDAG(dag_id="test_dag")
        
        task1 = MockOperator("task1", dag, return_value="result1")
        task2 = MockOperator("task2", dag, return_value="result2")
        
        task1 >> task2
        
        # 执行DAG
        success = await dag.run()
        
        assert success == True
        assert task1.executed == True
        assert task2.executed == True
    
    async def test_dag_execution_with_failure(self):
        """测试DAG执行失败情况"""
        dag = BaseDAG(dag_id="test_dag")
        
        task1 = MockOperator("task1", dag, return_value="result1")
        task2 = MockOperator("task2", dag, should_fail=True)
        task3 = MockOperator("task3", dag, return_value="result3")
        
        task1 >> task2 >> task3
        
        # 执行DAG
        success = await dag.run()
        
        assert success == False
        assert task1.executed == True
        assert task2.executed == True
        assert task3.executed == False  # 不应该执行，因为task2失败了


@pytest.mark.asyncio
class TestWorkflowOperators:
    """测试工作流操作器"""
    
    @patch('data_trans.crawlers.http_crawler.HTTPCrawler')
    async def test_crawler_operator(self, mock_crawler_class):
        """测试爬虫操作器"""
        # 模拟爬虫
        mock_crawler = Mock()
        mock_crawler.crawl.return_value = {"data": "test_data"}
        mock_crawler_class.return_value = mock_crawler
        
        dag = BaseDAG(dag_id="test_dag")
        
        crawler_task = CrawlerOperator(
            task_id="test_crawler",
            dag=dag,
            crawler_config={"timeout": 30},
            target_urls=["http://example.com"],
            output_key="crawl_result"
        )
        
        context = {
            'dag': dag,
            'task': crawler_task,
            'task_instance': Mock(),
            'execution_date': datetime.now()
        }
        
        # 模拟task_instance的log_info方法
        context['task_instance'].log_info = Mock()
        
        result = await crawler_task.execute(context)
        
        assert result is not None
        assert 'results' in result
        assert 'summary' in result
    
    @patch('data_trans.cleaners.base.DataCleaner')
    async def test_cleaner_operator(self, mock_cleaner_class):
        """测试清洗操作器"""
        # 模拟清洗器
        mock_cleaner = Mock()
        mock_cleaner.clean.return_value = {"cleaned": "data"}
        mock_cleaner_class.return_value = mock_cleaner
        
        dag = BaseDAG(dag_id="test_dag")
        
        # 先设置XCom数据
        crawl_data = {
            'results': [
                {'url': 'http://example.com', 'data': {'raw': 'data'}, 'status': 'success'}
            ]
        }
        XCom.push("crawl_result", crawl_data, "crawler", "test_dag")
        
        cleaner_task = CleanerOperator(
            task_id="test_cleaner",
            dag=dag,
            cleaner_config={"remove_html": True},
            input_key="crawl_result",
            output_key="clean_result"
        )
        
        cleaner_task.upstream_task_ids = ["crawler"]
        
        context = {
            'dag': dag,
            'task': cleaner_task,
            'task_instance': Mock(),
            'execution_date': datetime.now()
        }
        
        context['task_instance'].log_info = Mock()
        
        result = await cleaner_task.execute(context)
        
        assert result is not None
        assert 'results' in result
        assert 'summary' in result


@pytest.mark.asyncio
class TestWorkflowSensors:
    """测试工作流传感器"""
    
    async def test_time_sensor(self):
        """测试时间传感器"""
        dag = BaseDAG(dag_id="test_dag")
        
        # 测试过去的时间（应该立即触发）
        past_time = (datetime.now() - timedelta(minutes=1)).strftime("%H:%M")
        
        time_sensor = TimeSensor(
            task_id="test_time_sensor",
            dag=dag,
            target_time=past_time,
            check_interval=1
        )
        
        context = {
            'dag': dag,
            'task': time_sensor,
            'task_instance': Mock(),
            'execution_date': datetime.now()
        }
        
        context['task_instance'].log_info = Mock()
        
        result = await time_sensor.poke(context)
        assert result == True
    
    @patch('httpx.AsyncClient')
    async def test_http_sensor(self, mock_client_class):
        """测试HTTP传感器"""
        from data_trans.workflow.sensors import HttpSensor
        
        # 模拟HTTP响应
        mock_response = Mock()
        mock_response.status_code = 200
        mock_client = Mock()
        mock_client.request.return_value = mock_response
        mock_client_class.return_value.__aenter__.return_value = mock_client
        
        dag = BaseDAG(dag_id="test_dag")
        
        http_sensor = HttpSensor(
            task_id="test_http_sensor",
            dag=dag,
            endpoint="http://example.com",
            expected_status=200
        )
        
        context = {
            'dag': dag,
            'task': http_sensor,
            'task_instance': Mock(),
            'execution_date': datetime.now()
        }
        
        context['task_instance'].log_info = Mock()
        
        result = await http_sensor.poke(context)
        assert result == True


@pytest.mark.asyncio
class TestWorkflowScheduler:
    """测试工作流调度器"""
    
    async def test_scheduler_dag_registration(self):
        """测试调度器DAG注册"""
        scheduler = WorkflowScheduler()
        dag = BaseDAG(dag_id="test_dag")
        
        scheduler.register_dag(dag)
        
        assert "test_dag" in scheduler.dags
        assert scheduler.get_dag("test_dag") == dag
        
        scheduler.unregister_dag("test_dag")
        assert "test_dag" not in scheduler.dags
    
    async def test_scheduler_dag_trigger(self):
        """测试调度器DAG触发"""
        scheduler = WorkflowScheduler()
        
        dag = BaseDAG(dag_id="test_dag")
        task = MockOperator("test_task", dag, return_value="success")
        
        scheduler.register_dag(dag)
        
        # 触发DAG
        run_id = await scheduler.trigger_dag("test_dag")
        
        assert run_id is not None
        assert run_id in scheduler.dag_runs
        
        # 等待执行完成
        await asyncio.sleep(0.1)
        
        dag_run = scheduler.get_dag_run(run_id)
        assert dag_run.state in [DAGRunState.SUCCESS, DAGRunState.RUNNING]
    
    async def test_scheduler_status(self):
        """测试调度器状态"""
        scheduler = WorkflowScheduler()
        
        status = scheduler.get_scheduler_status()
        
        assert 'running' in status
        assert 'registered_dags' in status
        assert 'total_runs' in status
        assert 'dag_runs_by_state' in status


@pytest.mark.asyncio
class TestWorkflowNotifications:
    """测试工作流通知"""
    
    async def test_log_notifier(self):
        """测试日志通知器"""
        from data_trans.workflow.notifications import LogNotifier
        
        notifier = LogNotifier({'enabled': True})
        
        result = await notifier.send("测试消息", "测试主题")
        assert result == True
    
    async def test_notification_manager(self):
        """测试通知管理器"""
        config = {
            'log': {'enabled': True}
        }
        
        manager = NotificationManager(config)
        
        results = await manager.send_notification(
            message="测试消息",
            subject="测试主题",
            channels=['log']
        )
        
        assert 'log' in results
        assert results['log'] == True


@pytest.mark.asyncio
class TestDAGTemplates:
    """测试DAG模板"""
    
    async def test_simple_crawl_dag_creation(self):
        """测试简单爬取DAG模板"""
        dag = create_simple_crawl_dag(
            dag_id="test_simple_crawl",
            target_urls=["http://example.com"],
            crawler_config={"timeout": 30},
            cleaner_config={"remove_html": True},
            storage_config={"type": "mongodb"}
        )
        
        assert dag.dag_id == "test_simple_crawl"
        assert len(dag.tasks) == 4  # crawler, cleaner, storage, notification
        
        # 检查任务依赖关系
        crawler_task = dag.get_task("crawler")
        cleaner_task = dag.get_task("cleaner")
        storage_task = dag.get_task("storage")
        notification_task = dag.get_task("notification")
        
        assert "cleaner" in crawler_task.downstream_task_ids
        assert "storage" in cleaner_task.downstream_task_ids
        assert "notification" in storage_task.downstream_task_ids


# 运行测试的主函数
async def run_workflow_tests():
    """运行工作流测试"""
    print("开始工作流系统测试...")
    
    # 基础功能测试
    test_base = TestWorkflowBase()
    await test_base.test_dag_creation()
    await test_base.test_task_dependencies()
    await test_base.test_xcom_data_exchange()
    await test_base.test_dag_validation()
    await test_base.test_dag_execution()
    await test_base.test_dag_execution_with_failure()
    print("✓ 基础功能测试通过")
    
    # 传感器测试
    test_sensors = TestWorkflowSensors()
    await test_sensors.test_time_sensor()
    print("✓ 传感器测试通过")
    
    # 调度器测试
    test_scheduler = TestWorkflowScheduler()
    await test_scheduler.test_scheduler_dag_registration()
    await test_scheduler.test_scheduler_dag_trigger()
    await test_scheduler.test_scheduler_status()
    print("✓ 调度器测试通过")
    
    # 通知测试
    test_notifications = TestWorkflowNotifications()
    await test_notifications.test_log_notifier()
    await test_notifications.test_notification_manager()
    print("✓ 通知系统测试通过")
    
    # DAG模板测试
    test_templates = TestDAGTemplates()
    await test_templates.test_simple_crawl_dag_creation()
    print("✓ DAG模板测试通过")
    
    print("所有工作流测试通过！")


if __name__ == "__main__":
    asyncio.run(run_workflow_tests())
