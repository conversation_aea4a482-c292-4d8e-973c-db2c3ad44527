{"data_mtime": 1752392498, "dep_lines": [7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 20], "dep_prios": [10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["asyncio", "logging", "time", "typing", "builtins", "_frozen_importlib", "_sitebuiltins", "_typeshed", "abc", "os", "types"], "hash": "fe84b036fa56e9e939a55276fa2f8769f95982d2", "id": "src.data_trans.executor.simple_ray_test", "ignore_all": false, "interface_hash": "67fdf0e1d812f573392c3161ce33ec35e068ca24", "mtime": 1752392685, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": true, "disable_error_code": [], "disable_memoryview_promotion": true, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": true, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": true, "disallow_untyped_calls": true, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": true, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": true, "ignore_missing_imports": true, "implicit_optional": true, "implicit_reexport": false, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": ["pydantic.mypy"], "strict_bytes": true, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": false, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": true}, "path": "src\\data_trans\\executor\\simple_ray_test.py", "plugin_data": [{"debug_dataclass_transform": false, "init_forbid_extra": true, "init_typed": true, "warn_required_dynamic_aliases": true}, null], "size": 7654, "suppressed": ["ray"], "version_id": "1.16.1"}