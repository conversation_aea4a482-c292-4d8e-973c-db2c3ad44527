"""
工作流调度器 - 管理DAG的执行和调度
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import uuid

from .base import BaseDAG, TaskState
from .notifications import NotificationManager, get_notification_manager

logger = logging.getLogger(__name__)


class DAGRunState(Enum):
    """DAG运行状态"""
    QUEUED = "queued"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class DAGRun:
    """DAG运行实例"""
    run_id: str
    dag_id: str
    execution_date: datetime
    state: DAGRunState
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    log_messages: List[str] = None
    
    def __post_init__(self):
        if self.log_messages is None:
            self.log_messages = []
    
    def add_log(self, message: str):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.log_messages.append(f"[{timestamp}] {message}")
        logger.info(f"[{self.dag_id}:{self.run_id}] {message}")


class WorkflowScheduler:
    """工作流调度器"""
    
    def __init__(self, notification_config: Optional[Dict] = None):
        self.dags: Dict[str, BaseDAG] = {}
        self.dag_runs: Dict[str, DAGRun] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.notification_manager = get_notification_manager(notification_config)
        self._scheduler_task: Optional[asyncio.Task] = None
        self._running = False
    
    def register_dag(self, dag: BaseDAG):
        """注册DAG"""
        self.dags[dag.dag_id] = dag
        logger.info(f"注册DAG: {dag.dag_id}")
    
    def unregister_dag(self, dag_id: str):
        """注销DAG"""
        if dag_id in self.dags:
            del self.dags[dag_id]
            logger.info(f"注销DAG: {dag_id}")
    
    def get_dag(self, dag_id: str) -> Optional[BaseDAG]:
        """获取DAG"""
        return self.dags.get(dag_id)
    
    def list_dags(self) -> List[BaseDAG]:
        """列出所有DAG"""
        return list(self.dags.values())
    
    async def trigger_dag(
        self,
        dag_id: str,
        execution_date: Optional[datetime] = None,
        run_id: Optional[str] = None
    ) -> Optional[str]:
        """手动触发DAG执行"""
        dag = self.get_dag(dag_id)
        if not dag:
            logger.error(f"DAG不存在: {dag_id}")
            return None
        
        execution_date = execution_date or datetime.now()
        run_id = run_id or f"manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        
        # 检查是否已有相同的运行实例
        if run_id in self.dag_runs:
            logger.warning(f"DAG运行实例已存在: {run_id}")
            return None
        
        # 创建DAG运行实例
        dag_run = DAGRun(
            run_id=run_id,
            dag_id=dag_id,
            execution_date=execution_date,
            state=DAGRunState.QUEUED
        )
        
        self.dag_runs[run_id] = dag_run
        dag_run.add_log(f"DAG运行实例已创建")
        
        # 异步执行DAG
        task = asyncio.create_task(self._execute_dag_run(dag_run))
        self.running_tasks[run_id] = task
        
        logger.info(f"触发DAG执行: {dag_id}, 运行ID: {run_id}")
        return run_id
    
    async def _execute_dag_run(self, dag_run: DAGRun):
        """执行DAG运行实例"""
        dag = self.get_dag(dag_run.dag_id)
        if not dag:
            dag_run.state = DAGRunState.FAILED
            dag_run.add_log("DAG不存在")
            return
        
        try:
            dag_run.state = DAGRunState.RUNNING
            dag_run.start_date = datetime.now()
            dag_run.add_log("开始执行DAG")
            
            # 执行DAG
            success = await dag.run(dag_run.execution_date)
            
            dag_run.end_date = datetime.now()
            
            if success:
                dag_run.state = DAGRunState.SUCCESS
                dag_run.add_log("DAG执行成功")
                
                # 发送成功通知
                await self.notification_manager.send_success_notification(
                    dag_id=dag_run.dag_id,
                    execution_date=dag_run.execution_date.isoformat()
                )
            else:
                dag_run.state = DAGRunState.FAILED
                dag_run.add_log("DAG执行失败")
                
                # 发送失败通知
                await self.notification_manager.send_failure_notification(
                    dag_id=dag_run.dag_id,
                    execution_date=dag_run.execution_date.isoformat(),
                    error="DAG执行返回失败状态"
                )
        
        except Exception as e:
            dag_run.state = DAGRunState.FAILED
            dag_run.end_date = datetime.now()
            error_msg = f"DAG执行异常: {str(e)}"
            dag_run.add_log(error_msg)
            
            # 发送失败通知
            await self.notification_manager.send_failure_notification(
                dag_id=dag_run.dag_id,
                execution_date=dag_run.execution_date.isoformat(),
                error=str(e)
            )
            
            logger.error(f"DAG执行异常: {dag_run.dag_id}, 错误: {e}")
        
        finally:
            # 清理运行任务
            if dag_run.run_id in self.running_tasks:
                del self.running_tasks[dag_run.run_id]
    
    async def cancel_dag_run(self, run_id: str) -> bool:
        """取消DAG运行"""
        if run_id not in self.dag_runs:
            logger.error(f"DAG运行实例不存在: {run_id}")
            return False
        
        dag_run = self.dag_runs[run_id]
        
        if dag_run.state not in [DAGRunState.QUEUED, DAGRunState.RUNNING]:
            logger.warning(f"DAG运行实例无法取消，当前状态: {dag_run.state}")
            return False
        
        # 取消运行任务
        if run_id in self.running_tasks:
            task = self.running_tasks[run_id]
            task.cancel()
            del self.running_tasks[run_id]
        
        dag_run.state = DAGRunState.CANCELLED
        dag_run.end_date = datetime.now()
        dag_run.add_log("DAG运行已取消")
        
        logger.info(f"取消DAG运行: {run_id}")
        return True
    
    def get_dag_run(self, run_id: str) -> Optional[DAGRun]:
        """获取DAG运行实例"""
        return self.dag_runs.get(run_id)
    
    def list_dag_runs(self, dag_id: Optional[str] = None, state: Optional[DAGRunState] = None) -> List[DAGRun]:
        """列出DAG运行实例"""
        runs = list(self.dag_runs.values())
        
        if dag_id:
            runs = [run for run in runs if run.dag_id == dag_id]
        
        if state:
            runs = [run for run in runs if run.state == state]
        
        return sorted(runs, key=lambda x: x.execution_date, reverse=True)
    
    async def start_scheduler(self, check_interval: int = 60):
        """启动调度器"""
        if self._running:
            logger.warning("调度器已在运行")
            return
        
        self._running = True
        self._scheduler_task = asyncio.create_task(self._scheduler_loop(check_interval))
        logger.info("工作流调度器已启动")
    
    async def stop_scheduler(self):
        """停止调度器"""
        if not self._running:
            return
        
        self._running = False
        
        if self._scheduler_task:
            self._scheduler_task.cancel()
            try:
                await self._scheduler_task
            except asyncio.CancelledError:
                pass
        
        # 取消所有运行中的任务
        for run_id in list(self.running_tasks.keys()):
            await self.cancel_dag_run(run_id)
        
        logger.info("工作流调度器已停止")
    
    async def _scheduler_loop(self, check_interval: int):
        """调度器主循环"""
        while self._running:
            try:
                await self._check_scheduled_dags()
                await asyncio.sleep(check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"调度器循环异常: {e}")
                await asyncio.sleep(check_interval)
    
    async def _check_scheduled_dags(self):
        """检查需要调度的DAG"""
        current_time = datetime.now()
        
        for dag in self.dags.values():
            if not dag.schedule_interval:
                continue
            
            # 简化的调度逻辑
            if dag.schedule_interval == "@daily":
                # 每天执行一次
                last_run = self._get_last_successful_run(dag.dag_id)
                if not last_run or (current_time - last_run.execution_date).days >= 1:
                    await self.trigger_dag(dag.dag_id)
            
            elif dag.schedule_interval == "@hourly":
                # 每小时执行一次
                last_run = self._get_last_successful_run(dag.dag_id)
                if not last_run or (current_time - last_run.execution_date).seconds >= 3600:
                    await self.trigger_dag(dag.dag_id)
    
    def _get_last_successful_run(self, dag_id: str) -> Optional[DAGRun]:
        """获取最后一次成功运行"""
        successful_runs = [
            run for run in self.dag_runs.values()
            if run.dag_id == dag_id and run.state == DAGRunState.SUCCESS
        ]
        
        if successful_runs:
            return max(successful_runs, key=lambda x: x.execution_date)
        
        return None
    
    def get_scheduler_status(self) -> Dict:
        """获取调度器状态"""
        return {
            "running": self._running,
            "registered_dags": len(self.dags),
            "total_runs": len(self.dag_runs),
            "running_tasks": len(self.running_tasks),
            "dag_runs_by_state": {
                state.value: len([run for run in self.dag_runs.values() if run.state == state])
                for state in DAGRunState
            }
        }
    
    async def cleanup_old_runs(self, days: int = 7):
        """清理旧的运行记录"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        old_runs = [
            run_id for run_id, run in self.dag_runs.items()
            if run.execution_date < cutoff_date and run.state in [DAGRunState.SUCCESS, DAGRunState.FAILED, DAGRunState.CANCELLED]
        ]
        
        for run_id in old_runs:
            del self.dag_runs[run_id]
        
        logger.info(f"清理了 {len(old_runs)} 个旧的运行记录")


# 全局调度器实例
_scheduler: Optional[WorkflowScheduler] = None


def get_scheduler(notification_config: Optional[Dict] = None) -> WorkflowScheduler:
    """获取调度器实例"""
    global _scheduler
    if _scheduler is None:
        _scheduler = WorkflowScheduler(notification_config)
    return _scheduler
