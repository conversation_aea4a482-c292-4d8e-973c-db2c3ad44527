"""
自定义操作器 - 封装爬虫、清洗和存储任务
"""

import asyncio
import logging
from typing import Any, Dict, Optional, List
from datetime import datetime

from .base import BaseOperator, XCom
from ..crawlers.base import BaseCrawler
from ..cleaners.base import DataCleaner
from ..storage.base import BaseStorage
from ..config.settings import get_settings

logger = logging.getLogger(__name__)


class CrawlerOperator(BaseOperator):
    """爬虫操作器"""
    
    def __init__(
        self,
        task_id: str,
        dag,
        crawler_config: Dict[str, Any],
        target_urls: Optional[List[str]] = None,
        output_key: str = "crawl_result",
        **kwargs
    ):
        super().__init__(task_id, dag, **kwargs)
        self.crawler_config = crawler_config
        self.target_urls = target_urls or []
        self.output_key = output_key
    
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行爬虫任务"""
        task_instance = context['task_instance']
        task_instance.log_info("开始执行爬虫任务")
        
        try:
            # 从上游任务获取URL（如果没有指定）
            if not self.target_urls:
                urls_from_upstream = XCom.pull(
                    key="target_urls",
                    task_id="url_generator",  # 假设有一个URL生成任务
                    dag_id=context['dag'].dag_id
                )
                self.target_urls = urls_from_upstream or []
            
            if not self.target_urls:
                raise ValueError("没有找到要爬取的URL")
            
            task_instance.log_info(f"准备爬取 {len(self.target_urls)} 个URL")
            
            # 创建爬虫实例
            from ..crawlers.http_crawler import HTTPCrawler
            crawler = HTTPCrawler(self.crawler_config)
            
            # 执行爬取
            results = []
            for i, url in enumerate(self.target_urls):
                task_instance.log_info(f"爬取URL {i+1}/{len(self.target_urls)}: {url}")
                
                try:
                    result = await crawler.crawl(url)
                    results.append({
                        'url': url,
                        'data': result,
                        'timestamp': datetime.now().isoformat(),
                        'status': 'success'
                    })
                    task_instance.log_info(f"成功爬取: {url}")
                except Exception as e:
                    task_instance.log_info(f"爬取失败: {url}, 错误: {str(e)}")
                    results.append({
                        'url': url,
                        'data': None,
                        'timestamp': datetime.now().isoformat(),
                        'status': 'failed',
                        'error': str(e)
                    })
            
            # 统计结果
            success_count = sum(1 for r in results if r['status'] == 'success')
            total_count = len(results)
            
            crawl_result = {
                'results': results,
                'summary': {
                    'total': total_count,
                    'success': success_count,
                    'failed': total_count - success_count,
                    'success_rate': success_count / total_count if total_count > 0 else 0
                },
                'execution_time': datetime.now().isoformat()
            }
            
            # 推送结果到XCom
            XCom.push(
                key=self.output_key,
                value=crawl_result,
                task_id=self.task_id,
                dag_id=context['dag'].dag_id
            )
            
            task_instance.log_info(f"爬虫任务完成，成功率: {crawl_result['summary']['success_rate']:.2%}")
            
            return crawl_result
            
        except Exception as e:
            task_instance.log_info(f"爬虫任务执行失败: {str(e)}")
            raise e


class CleanerOperator(BaseOperator):
    """数据清洗操作器"""
    
    def __init__(
        self,
        task_id: str,
        dag,
        cleaner_config: Dict[str, Any],
        input_key: str = "crawl_result",
        output_key: str = "clean_result",
        **kwargs
    ):
        super().__init__(task_id, dag, **kwargs)
        self.cleaner_config = cleaner_config
        self.input_key = input_key
        self.output_key = output_key
    
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据清洗任务"""
        task_instance = context['task_instance']
        task_instance.log_info("开始执行数据清洗任务")
        
        try:
            # 从上游任务获取数据
            crawl_data = XCom.pull(
                key=self.input_key,
                task_id=self.upstream_task_ids[0] if self.upstream_task_ids else "crawler",
                dag_id=context['dag'].dag_id
            )
            
            if not crawl_data:
                raise ValueError("没有找到要清洗的数据")
            
            task_instance.log_info(f"准备清洗 {len(crawl_data.get('results', []))} 条数据")
            
            # 创建清洗器实例
            cleaner = DataCleaner(self.cleaner_config)
            
            # 执行清洗
            cleaned_results = []
            raw_results = crawl_data.get('results', [])
            
            for i, item in enumerate(raw_results):
                if item['status'] != 'success' or not item['data']:
                    continue
                
                task_instance.log_info(f"清洗数据 {i+1}/{len(raw_results)}")
                
                try:
                    cleaned_data = await cleaner.clean(item['data'])
                    cleaned_results.append({
                        'url': item['url'],
                        'original_data': item['data'],
                        'cleaned_data': cleaned_data,
                        'timestamp': datetime.now().isoformat(),
                        'status': 'success'
                    })
                    task_instance.log_info(f"成功清洗数据: {item['url']}")
                except Exception as e:
                    task_instance.log_info(f"清洗失败: {item['url']}, 错误: {str(e)}")
                    cleaned_results.append({
                        'url': item['url'],
                        'original_data': item['data'],
                        'cleaned_data': None,
                        'timestamp': datetime.now().isoformat(),
                        'status': 'failed',
                        'error': str(e)
                    })
            
            # 统计结果
            success_count = sum(1 for r in cleaned_results if r['status'] == 'success')
            total_count = len(cleaned_results)
            
            clean_result = {
                'results': cleaned_results,
                'summary': {
                    'total': total_count,
                    'success': success_count,
                    'failed': total_count - success_count,
                    'success_rate': success_count / total_count if total_count > 0 else 0
                },
                'execution_time': datetime.now().isoformat()
            }
            
            # 推送结果到XCom
            XCom.push(
                key=self.output_key,
                value=clean_result,
                task_id=self.task_id,
                dag_id=context['dag'].dag_id
            )
            
            task_instance.log_info(f"数据清洗完成，成功率: {clean_result['summary']['success_rate']:.2%}")
            
            return clean_result
            
        except Exception as e:
            task_instance.log_info(f"数据清洗任务执行失败: {str(e)}")
            raise e


class StorageOperator(BaseOperator):
    """数据存储操作器"""
    
    def __init__(
        self,
        task_id: str,
        dag,
        storage_config: Dict[str, Any],
        input_key: str = "clean_result",
        storage_type: str = "mongodb",
        collection_name: Optional[str] = None,
        **kwargs
    ):
        super().__init__(task_id, dag, **kwargs)
        self.storage_config = storage_config
        self.input_key = input_key
        self.storage_type = storage_type
        self.collection_name = collection_name
    
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行数据存储任务"""
        task_instance = context['task_instance']
        task_instance.log_info("开始执行数据存储任务")
        
        try:
            # 从上游任务获取数据
            clean_data = XCom.pull(
                key=self.input_key,
                task_id=self.upstream_task_ids[0] if self.upstream_task_ids else "cleaner",
                dag_id=context['dag'].dag_id
            )
            
            if not clean_data:
                raise ValueError("没有找到要存储的数据")
            
            cleaned_results = clean_data.get('results', [])
            success_data = [r for r in cleaned_results if r['status'] == 'success' and r['cleaned_data']]
            
            if not success_data:
                task_instance.log_info("没有成功清洗的数据需要存储")
                return {'stored_count': 0, 'message': 'No data to store'}
            
            task_instance.log_info(f"准备存储 {len(success_data)} 条数据")
            
            # 创建存储实例
            if self.storage_type == "mongodb":
                from ..storage.mongodb_storage import MongoDBStorage
                storage = MongoDBStorage(self.storage_config)
            elif self.storage_type == "postgresql":
                from ..storage.postgresql_storage import PostgreSQLStorage
                storage = PostgreSQLStorage(self.storage_config)
            else:
                raise ValueError(f"不支持的存储类型: {self.storage_type}")
            
            # 准备存储数据
            documents = []
            for item in success_data:
                doc = {
                    'url': item['url'],
                    'data': item['cleaned_data'],
                    'crawl_timestamp': item['timestamp'],
                    'stored_timestamp': datetime.now().isoformat(),
                    'dag_id': context['dag'].dag_id,
                    'task_id': self.task_id
                }
                documents.append(doc)
            
            # 执行存储
            collection = self.collection_name or f"crawl_data_{context['dag'].dag_id}"
            stored_count = await storage.store_batch(documents, collection)
            
            storage_result = {
                'stored_count': stored_count,
                'total_count': len(success_data),
                'collection': collection,
                'storage_type': self.storage_type,
                'execution_time': datetime.now().isoformat()
            }
            
            # 推送结果到XCom
            XCom.push(
                key="storage_result",
                value=storage_result,
                task_id=self.task_id,
                dag_id=context['dag'].dag_id
            )
            
            task_instance.log_info(f"数据存储完成，存储了 {stored_count} 条数据到 {collection}")
            
            return storage_result
            
        except Exception as e:
            task_instance.log_info(f"数据存储任务执行失败: {str(e)}")
            raise e


class NotificationOperator(BaseOperator):
    """通知操作器"""
    
    def __init__(
        self,
        task_id: str,
        dag,
        notification_config: Dict[str, Any],
        message_template: str = "DAG {dag_id} 执行完成",
        **kwargs
    ):
        super().__init__(task_id, dag, **kwargs)
        self.notification_config = notification_config
        self.message_template = message_template
    
    async def execute(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行通知任务"""
        task_instance = context['task_instance']
        task_instance.log_info("开始执行通知任务")
        
        try:
            # 收集执行结果
            dag_id = context['dag'].dag_id
            execution_date = context['execution_date']
            
            # 获取各阶段结果
            crawl_result = XCom.pull(key="crawl_result", task_id="crawler", dag_id=dag_id)
            clean_result = XCom.pull(key="clean_result", task_id="cleaner", dag_id=dag_id)
            storage_result = XCom.pull(key="storage_result", task_id="storage", dag_id=dag_id)
            
            # 构建消息
            message = self.message_template.format(
                dag_id=dag_id,
                execution_date=execution_date,
                crawl_summary=crawl_result.get('summary', {}) if crawl_result else {},
                clean_summary=clean_result.get('summary', {}) if clean_result else {},
                storage_summary=storage_result if storage_result else {}
            )
            
            # 发送通知（这里简化为日志记录）
            task_instance.log_info(f"发送通知: {message}")
            logger.info(f"工作流通知: {message}")
            
            notification_result = {
                'message': message,
                'sent_at': datetime.now().isoformat(),
                'channels': self.notification_config.get('channels', ['log'])
            }
            
            return notification_result
            
        except Exception as e:
            task_instance.log_info(f"通知任务执行失败: {str(e)}")
            raise e
