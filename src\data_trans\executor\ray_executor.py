"""
Ray分布式任务执行器实现

基于Ray框架实现分布式任务执行，支持爬虫和清洗任务的分布式处理。
充分利用Python 3.11的性能优化和新特性。
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import structlog
from pydantic import BaseModel, Field

from ..config.settings import get_settings
from .base_executor import (
    BaseExecutor,
    ExecutionResult,
    ExecutionStatus,
    ExecutorConfig,
)
from .task_executor import TaskConfig, TaskExecutor, TaskExecutorConfig

# 配置结构化日志
logger = structlog.get_logger(__name__)

# Ray导入和错误处理
try:
    import ray
    from ray import remote

    RAY_AVAILABLE = True
    logger.info("Ray successfully imported", version=ray.__version__)
except ImportError as e:
    RAY_AVAILABLE = False
    logger.warning("Ray not available, falling back to local execution", error=str(e))

    # 创建Ray的模拟装饰器
    def remote(*args, **kwargs):
        """Ray remote装饰器的模拟实现"""

        def decorator(func):
            func._is_ray_remote = True
            func._ray_options = kwargs
            return func

        return decorator


class RayExecutorConfig(TaskExecutorConfig):
    """Ray执行器配置"""

    # Ray集群配置
    ray_address: Optional[str] = Field(
        default=None, description="Ray集群地址，None表示本地模式"
    )
    ray_runtime_env: Dict[str, Any] = Field(
        default_factory=dict, description="Ray运行时环境"
    )
    ray_namespace: str = Field(default="data_trans", description="Ray命名空间")

    # 资源配置
    default_cpu_per_task: float = Field(default=1.0, description="每个任务默认CPU资源")
    default_memory_per_task: int = Field(
        default=512, description="每个任务默认内存(MB)"
    )
    max_ray_workers: int = Field(default=4, description="最大Ray工作进程数")

    # 容错配置
    ray_task_max_retries: int = Field(default=3, description="Ray任务最大重试次数")
    ray_task_retry_delay: float = Field(default=1.0, description="Ray任务重试延迟")

    # 性能优化
    enable_ray_object_store: bool = Field(default=True, description="启用Ray对象存储")
    ray_batch_size: int = Field(default=10, description="Ray批处理大小")

    # 回退配置
    fallback_to_local: bool = Field(
        default=True, description="Ray不可用时回退到本地执行"
    )


class RayTaskResult(BaseModel):
    """Ray任务结果"""

    task_id: str = Field(description="任务ID")
    success: bool = Field(description="是否成功")
    result: Optional[Dict[str, Any]] = Field(default=None, description="执行结果")
    error: Optional[str] = Field(default=None, description="错误信息")
    execution_time: float = Field(description="执行时间")
    worker_id: Optional[str] = Field(default=None, description="工作节点ID")


# Ray远程函数定义
@remote
async def remote_crawl_task(
    task_config: Dict[str, Any], settings_dict: Dict[str, Any]
) -> RayTaskResult:
    """Ray远程爬虫任务

    Args:
        task_config: 任务配置
        settings_dict: 系统设置

    Returns:
        任务执行结果
    """
    start_time = time.time()
    task_id = task_config.get("task_id", "unknown")

    try:
        logger.info("开始执行远程爬虫任务", task_id=task_id)

        # 在Ray worker中重新创建TaskExecutor
        from ..config.settings import Settings
        from .task_executor import TaskExecutor, TaskExecutorConfig

        # 重建设置对象
        Settings(**settings_dict)

        # 创建执行器配置
        executor_config = TaskExecutorConfig()
        executor = TaskExecutor(executor_config)

        # 执行任务
        result = await executor.execute_task(task_id, task_config)

        execution_time = time.time() - start_time

        return RayTaskResult(
            task_id=task_id,
            success=result.status == ExecutionStatus.SUCCESS,
            result={
                "crawled_count": result.crawled_count,
                "cleaned_count": result.cleaned_count,
                "stored_count": result.stored_count,
                "logs": result.logs[-10:],  # 只保留最后10条日志
                "metrics": result.metrics,
            },
            execution_time=execution_time,
            worker_id=(
                ray.get_runtime_context().get_worker_id() if RAY_AVAILABLE else "local"
            ),
        )

    except Exception as e:
        execution_time = time.time() - start_time
        logger.error("远程爬虫任务执行失败", task_id=task_id, error=str(e))

        return RayTaskResult(
            task_id=task_id,
            success=False,
            error=str(e),
            execution_time=execution_time,
            worker_id=(
                ray.get_runtime_context().get_worker_id() if RAY_AVAILABLE else "local"
            ),
        )


@remote
async def remote_clean_task(
    raw_data: List[Dict[str, Any]], clean_config: Dict[str, Any], task_id: str
) -> RayTaskResult:
    """Ray远程数据清洗任务

    Args:
        raw_data: 原始数据
        clean_config: 清洗配置
        task_id: 任务ID

    Returns:
        清洗结果
    """
    start_time = time.time()

    try:
        logger.info("开始执行远程清洗任务", task_id=task_id, data_count=len(raw_data))

        from ..cleaners.text_cleaner import TextCleaner, TextCleanerConfig

        # 创建清洗器
        cleaner_config = TextCleanerConfig(**clean_config.get("cleaner_config", {}))
        cleaner = TextCleaner(cleaner_config)

        # 添加清洗规则
        for rule_config in clean_config.get("cleaning_rules", []):
            cleaner.add_rule(**rule_config)

        cleaned_data = []

        # 清洗数据
        for item in raw_data:
            try:
                data_to_clean = item.get("data", {})
                if isinstance(data_to_clean, dict):
                    clean_result = await cleaner.clean_single(data_to_clean)

                    if clean_result.success:
                        cleaned_item = {
                            "original_url": item.get("url"),
                            "crawled_at": item.get("crawled_at"),
                            "cleaned_data": clean_result.cleaned_data,
                            "cleaning_metadata": {
                                "applied_rules": clean_result.applied_rules,
                                "warnings": clean_result.warnings,
                                "processing_time": clean_result.processing_time,
                            },
                            "cleaned_at": datetime.utcnow().isoformat(),
                        }
                        cleaned_data.append(cleaned_item)

            except Exception as e:
                logger.warning("单条数据清洗失败", error=str(e))

        execution_time = time.time() - start_time

        return RayTaskResult(
            task_id=task_id,
            success=True,
            result={
                "cleaned_data": cleaned_data,
                "original_count": len(raw_data),
                "cleaned_count": len(cleaned_data),
            },
            execution_time=execution_time,
            worker_id=(
                ray.get_runtime_context().get_worker_id() if RAY_AVAILABLE else "local"
            ),
        )

    except Exception as e:
        execution_time = time.time() - start_time
        logger.error("远程清洗任务执行失败", task_id=task_id, error=str(e))

        return RayTaskResult(
            task_id=task_id,
            success=False,
            error=str(e),
            execution_time=execution_time,
            worker_id=(
                ray.get_runtime_context().get_worker_id() if RAY_AVAILABLE else "local"
            ),
        )


class RayExecutor(BaseExecutor):
    """Ray分布式任务执行器

    基于Ray框架实现分布式任务执行，支持任务分发到Ray集群。
    在Ray不可用时自动回退到本地TaskExecutor执行。
    """

    def __init__(self, config: RayExecutorConfig) -> None:
        """初始化Ray执行器

        Args:
            config: Ray执行器配置
        """
        super().__init__(config)
        self.config: RayExecutorConfig = config
        self.settings = get_settings()

        # Ray集群状态
        self._ray_initialized = False
        self._ray_cluster_info: Optional[Dict[str, Any]] = None

        # 本地执行器作为回退
        self._local_executor: Optional[TaskExecutor] = None

        # 初始化Ray集群
        asyncio.create_task(self._initialize_ray())

    async def _initialize_ray(self) -> None:
        """初始化Ray集群"""
        if not RAY_AVAILABLE:
            logger.warning("Ray不可用，将使用本地执行器")
            await self._initialize_local_executor()
            return

        try:
            # 检查Ray是否已经初始化
            if ray.is_initialized():
                logger.info("Ray已经初始化")
                self._ray_initialized = True
                self._ray_cluster_info = ray.cluster_resources()
                return

            # 初始化Ray
            init_config = {
                "namespace": self.config.ray_namespace,
                "runtime_env": self.config.ray_runtime_env,
                "ignore_reinit_error": True,
                "log_to_driver": False,
            }

            if self.config.ray_address:
                init_config["address"] = self.config.ray_address
                logger.info("连接到Ray集群", address=self.config.ray_address)
            else:
                init_config["local_mode"] = True
                logger.info("启动本地Ray集群")

            ray.init(**init_config)

            self._ray_initialized = True
            self._ray_cluster_info = ray.cluster_resources()

            logger.info(
                "Ray集群初始化成功",
                resources=self._ray_cluster_info,
                namespace=self.config.ray_namespace,
            )

        except Exception as e:
            logger.error("Ray集群初始化失败", error=str(e))

            if self.config.fallback_to_local:
                logger.info("回退到本地执行器")
                await self._initialize_local_executor()
            else:
                raise RuntimeError(f"Ray集群初始化失败且未启用本地回退: {e}")

    async def _initialize_local_executor(self) -> None:
        """初始化本地执行器作为回退"""
        if not self._local_executor:
            local_config = TaskExecutorConfig(
                max_concurrent_tasks=self.config.max_concurrent_tasks,
                timeout=self.config.timeout,
                max_retries=self.config.max_retries,
                retry_delay=self.config.retry_delay,
            )
            self._local_executor = TaskExecutor(local_config)
            logger.info("本地执行器初始化完成")

    async def execute_task(
        self, task_id: str, task_config: Dict[str, Any]
    ) -> ExecutionResult:
        """执行单个任务

        Args:
            task_id: 任务ID
            task_config: 任务配置

        Returns:
            执行结果
        """
        # 如果Ray不可用或未初始化，使用本地执行器
        if not self._ray_initialized or not RAY_AVAILABLE:
            if not self._local_executor:
                await self._initialize_local_executor()
            return await self._local_executor.execute_task(task_id, task_config)

        # 使用执行上下文管理器
        async with self.execution_context(task_id) as result:
            try:
                # 设置超时
                await asyncio.wait_for(
                    self._execute_ray_task(task_config, result),
                    timeout=self.config.timeout,
                )
            except asyncio.TimeoutError:
                result.status = ExecutionStatus.TIMEOUT
                result.set_error(f"Ray任务执行超时 ({self.config.timeout}秒)")
            except Exception as e:
                result.set_error(f"Ray任务执行异常: {e}")

        return result

    async def _execute_ray_task(
        self, task_config: Dict[str, Any], result: ExecutionResult
    ) -> None:
        """执行Ray分布式任务

        Args:
            task_config: 任务配置
            result: 执行结果对象
        """
        task_id = task_config.get("task_id", result.task_id)
        logger.info("开始执行Ray分布式任务", task_id=task_id)
        result.add_log("开始Ray分布式任务执行")

        try:
            # 准备设置字典（用于Ray worker）
            settings_dict = self.settings.model_dump()

            # 提交Ray任务
            if RAY_AVAILABLE:
                ray_task = remote_crawl_task.remote(task_config, settings_dict)
                ray_result = await ray_task
            else:
                # 本地模拟执行
                ray_result = await remote_crawl_task(task_config, settings_dict)

            # 处理Ray任务结果
            if ray_result.success:
                result.status = ExecutionStatus.SUCCESS
                result.crawled_count = ray_result.result.get("crawled_count", 0)
                result.cleaned_count = ray_result.result.get("cleaned_count", 0)
                result.stored_count = ray_result.result.get("stored_count", 0)

                # 合并日志
                for log_entry in ray_result.result.get("logs", []):
                    result.add_log(f"[Ray Worker] {log_entry}")

                # 合并指标
                result.metrics.update(ray_result.result.get("metrics", {}))
                result.metrics["ray_execution_time"] = ray_result.execution_time
                result.metrics["ray_worker_id"] = ray_result.worker_id

                logger.info(
                    "Ray任务执行成功",
                    task_id=task_id,
                    execution_time=ray_result.execution_time,
                    worker_id=ray_result.worker_id,
                )
                result.add_log(
                    f"Ray任务执行成功，耗时: {ray_result.execution_time:.2f}秒"
                )

            else:
                result.set_error(f"Ray任务执行失败: {ray_result.error}")
                logger.error(
                    "Ray任务执行失败",
                    task_id=task_id,
                    error=ray_result.error,
                    execution_time=ray_result.execution_time,
                )

        except Exception as e:
            logger.error("Ray任务提交失败", task_id=task_id, error=str(e))
            result.set_error(f"Ray任务提交失败: {e}")

    async def execute_batch_distributed(
        self, tasks: List[Dict[str, Any]]
    ) -> List[ExecutionResult]:
        """分布式批量执行任务

        Args:
            tasks: 任务列表

        Returns:
            执行结果列表
        """
        if not self._ray_initialized or not RAY_AVAILABLE:
            logger.warning("Ray不可用，使用本地批量执行")
            return await self.execute_batch(tasks)

        logger.info("开始Ray分布式批量任务执行", task_count=len(tasks))

        # 准备设置字典
        settings_dict = self.settings.model_dump()

        # 提交所有Ray任务
        ray_futures = []
        task_ids = []

        for task in tasks:
            task_id = task.get("task_id", f"batch_task_{int(time.time())}")
            task_ids.append(task_id)

            if RAY_AVAILABLE:
                future = remote_crawl_task.remote(task, settings_dict)
            else:
                future = remote_crawl_task(task, settings_dict)
            ray_futures.append(future)

        # 等待所有任务完成
        try:
            if RAY_AVAILABLE:
                ray_results = await asyncio.gather(*ray_futures)
            else:
                ray_results = await asyncio.gather(*ray_futures)

        except Exception as e:
            logger.error("Ray批量任务执行异常", error=str(e))
            # 创建失败结果
            ray_results = [
                RayTaskResult(
                    task_id=task_id, success=False, error=str(e), execution_time=0.0
                )
                for task_id in task_ids
            ]

        # 转换为ExecutionResult
        results = []
        for i, (ray_result, task_id) in enumerate(zip(ray_results, task_ids)):
            result = ExecutionResult(
                task_id=task_id,
                status=(
                    ExecutionStatus.SUCCESS
                    if ray_result.success
                    else ExecutionStatus.FAILED
                ),
                start_time=datetime.utcnow(),
                error_message=ray_result.error if not ray_result.success else None,
            )

            if ray_result.success and ray_result.result:
                result.crawled_count = ray_result.result.get("crawled_count", 0)
                result.cleaned_count = ray_result.result.get("cleaned_count", 0)
                result.stored_count = ray_result.result.get("stored_count", 0)
                result.metrics = ray_result.result.get("metrics", {})
                result.metrics["ray_execution_time"] = ray_result.execution_time
                result.metrics["ray_worker_id"] = ray_result.worker_id

            result.complete()
            results.append(result)

        logger.info(
            "Ray分布式批量任务完成",
            total=len(results),
            success=sum(1 for r in results if r.status == ExecutionStatus.SUCCESS),
            failed=sum(1 for r in results if r.status == ExecutionStatus.FAILED),
        )

        return results

    async def execute_distributed_cleaning(
        self,
        raw_data_batches: List[List[Dict[str, Any]]],
        clean_config: Dict[str, Any],
        task_id_prefix: str = "clean_batch",
    ) -> List[RayTaskResult]:
        """分布式数据清洗

        Args:
            raw_data_batches: 原始数据批次列表
            clean_config: 清洗配置
            task_id_prefix: 任务ID前缀

        Returns:
            清洗结果列表
        """
        if not self._ray_initialized or not RAY_AVAILABLE:
            logger.warning("Ray不可用，使用本地清洗")
            # 本地清洗实现
            results = []
            for i, batch in enumerate(raw_data_batches):
                task_id = f"{task_id_prefix}_{i}"
                result = await remote_clean_task(batch, clean_config, task_id)
                results.append(result)
            return results

        logger.info("开始Ray分布式数据清洗", batch_count=len(raw_data_batches))

        # 提交清洗任务
        ray_futures = []
        for i, batch in enumerate(raw_data_batches):
            task_id = f"{task_id_prefix}_{i}"
            if RAY_AVAILABLE:
                future = remote_clean_task.remote(batch, clean_config, task_id)
            else:
                future = remote_clean_task(batch, clean_config, task_id)
            ray_futures.append(future)

        # 等待所有清洗任务完成
        try:
            if RAY_AVAILABLE:
                results = await asyncio.gather(*ray_futures)
            else:
                results = await asyncio.gather(*ray_futures)

            logger.info(
                "Ray分布式清洗完成",
                batch_count=len(results),
                success_count=sum(1 for r in results if r.success),
            )

            return results

        except Exception as e:
            logger.error("Ray分布式清洗失败", error=str(e))
            raise

    def get_cluster_info(self) -> Dict[str, Any]:
        """获取Ray集群信息

        Returns:
            集群信息字典
        """
        if not self._ray_initialized or not RAY_AVAILABLE:
            return {
                "status": "not_available",
                "ray_available": RAY_AVAILABLE,
                "initialized": self._ray_initialized,
                "fallback_mode": True,
            }

        try:
            cluster_resources = ray.cluster_resources()
            node_info = ray.nodes()

            return {
                "status": "available",
                "ray_available": RAY_AVAILABLE,
                "initialized": self._ray_initialized,
                "namespace": self.config.ray_namespace,
                "address": self.config.ray_address,
                "cluster_resources": cluster_resources,
                "node_count": len(node_info),
                "nodes": [
                    {
                        "node_id": node["NodeID"],
                        "alive": node["Alive"],
                        "resources": node.get("Resources", {}),
                        "node_name": node.get("NodeName", "unknown"),
                    }
                    for node in node_info
                ],
                "fallback_mode": False,
            }

        except Exception as e:
            logger.error("获取Ray集群信息失败", error=str(e))
            return {"status": "error", "error": str(e), "fallback_mode": True}

    def get_ray_dashboard_url(self) -> Optional[str]:
        """获取Ray Dashboard URL

        Returns:
            Dashboard URL或None
        """
        if not self._ray_initialized or not RAY_AVAILABLE:
            return None

        try:
            # Ray Dashboard通常在8265端口
            if self.config.ray_address:
                # 从集群地址推断Dashboard地址
                if ":" in self.config.ray_address:
                    host = self.config.ray_address.split(":")[0]
                    return f"http://{host}:8265"
            else:
                return "http://localhost:8265"

        except Exception as e:
            logger.warning("无法获取Ray Dashboard URL", error=str(e))
            return None

    async def scale_cluster(self, target_workers: int) -> bool:
        """动态扩缩容Ray集群

        Args:
            target_workers: 目标工作节点数

        Returns:
            是否成功
        """
        if not self._ray_initialized or not RAY_AVAILABLE:
            logger.warning("Ray不可用，无法扩缩容")
            return False

        try:
            logger.info("开始Ray集群扩缩容", target_workers=target_workers)

            # 这里可以集成Ray Autoscaler或手动管理节点
            # 目前只记录日志，实际实现需要根据部署环境调整

            current_nodes = len(ray.nodes())
            logger.info(
                "当前集群状态",
                current_nodes=current_nodes,
                target_workers=target_workers,
            )

            # 模拟扩缩容成功
            return True

        except Exception as e:
            logger.error("Ray集群扩缩容失败", error=str(e))
            return False

    async def cleanup(self) -> None:
        """清理资源"""
        await super().cleanup()

        # 清理本地执行器
        if self._local_executor:
            await self._local_executor.cleanup()

        # 关闭Ray集群（仅在本地模式下）
        if self._ray_initialized and RAY_AVAILABLE:
            try:
                if not self.config.ray_address:  # 本地模式
                    ray.shutdown()
                    logger.info("Ray本地集群已关闭")
                else:
                    logger.info("保持Ray集群连接（远程模式）")

            except Exception as e:
                logger.warning("Ray集群关闭失败", error=str(e))

        logger.info("Ray执行器资源清理完成")
