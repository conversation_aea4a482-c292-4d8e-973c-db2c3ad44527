#!/usr/bin/env python3
"""
启动Airflow Webserver
"""

import os
import sys
import subprocess
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
AIRFLOW_HOME = PROJECT_ROOT / "airflow"

def setup_environment():
    """设置环境变量"""
    os.environ["AIRFLOW_HOME"] = str(AIRFLOW_HOME)
    os.environ["AIRFLOW__CORE__DAGS_FOLDER"] = str(AIRFLOW_HOME / "dags")
    os.environ["AIRFLOW__CORE__BASE_LOG_FOLDER"] = str(AIRFLOW_HOME / "logs")
    os.environ["AIRFLOW__CORE__PLUGINS_FOLDER"] = str(AIRFLOW_HOME / "plugins")
    os.environ["AIRFLOW__CORE__EXECUTOR"] = "SequentialExecutor"
    os.environ["AIRFLOW__DATABASE__SQL_ALCHEMY_CONN"] = "sqlite:////" + str(AIRFLOW_HOME / "airflow.db")
    os.environ["AIRFLOW__CORE__LOAD_EXAMPLES"] = "False"
    os.environ["AIRFLOW__WEBSERVER__EXPOSE_CONFIG"] = "True"

def start_webserver():
    """启动Airflow webserver"""
    setup_environment()
    
    print("启动Airflow Webserver...")
    print("Web UI地址: http://localhost:8080")
    print("用户名: admin")
    print("密码: admin123")
    print("按 Ctrl+C 停止服务")
    
    try:
        subprocess.run([
            sys.executable, "-m", "airflow", "webserver", "--port", "8080"
        ], cwd=PROJECT_ROOT)
    except KeyboardInterrupt:
        print("\nAirflow Webserver已停止")

if __name__ == "__main__":
    start_webserver()
