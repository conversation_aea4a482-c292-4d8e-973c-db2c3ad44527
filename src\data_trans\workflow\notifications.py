"""
通知系统 - 支持多种通知渠道
"""

import asyncio
import logging
import smtplib
from abc import ABC, abstractmethod
from datetime import datetime
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from typing import Dict, Any, List, Optional
import json

logger = logging.getLogger(__name__)


class BaseNotifier(ABC):
    """基础通知器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.enabled = config.get('enabled', True)
    
    @abstractmethod
    async def send(self, message: str, subject: str = "", **kwargs) -> bool:
        """发送通知"""
        pass
    
    def is_enabled(self) -> bool:
        """检查是否启用"""
        return self.enabled


class LogNotifier(BaseNotifier):
    """日志通知器"""
    
    async def send(self, message: str, subject: str = "", **kwargs) -> bool:
        """发送日志通知"""
        try:
            log_level = kwargs.get('level', 'info').lower()
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            log_message = f"[{timestamp}] {subject}: {message}" if subject else f"[{timestamp}] {message}"
            
            if log_level == 'error':
                logger.error(log_message)
            elif log_level == 'warning':
                logger.warning(log_message)
            else:
                logger.info(log_message)
            
            return True
        except Exception as e:
            logger.error(f"日志通知发送失败: {e}")
            return False


class EmailNotifier(BaseNotifier):
    """邮件通知器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.smtp_host = config.get('smtp_host', 'localhost')
        self.smtp_port = config.get('smtp_port', 587)
        self.smtp_user = config.get('smtp_user', '')
        self.smtp_password = config.get('smtp_password', '')
        self.from_email = config.get('from_email', '<EMAIL>')
        self.recipients = config.get('recipients', [])
        self.use_tls = config.get('use_tls', True)
    
    async def send(self, message: str, subject: str = "DataTrans通知", **kwargs) -> bool:
        """发送邮件通知"""
        if not self.recipients:
            logger.warning("邮件通知：没有配置收件人")
            return False
        
        try:
            # 创建邮件
            msg = MimeMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.recipients)
            msg['Subject'] = subject
            
            # 添加邮件内容
            body = MimeText(message, 'plain', 'utf-8')
            msg.attach(body)
            
            # 发送邮件
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls()
                
                if self.smtp_user and self.smtp_password:
                    server.login(self.smtp_user, self.smtp_password)
                
                server.send_message(msg)
            
            logger.info(f"邮件通知发送成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"邮件通知发送失败: {e}")
            return False


class SlackNotifier(BaseNotifier):
    """Slack通知器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.webhook_url = config.get('webhook_url', '')
        self.channel = config.get('channel', '#general')
        self.username = config.get('username', 'DataTrans')
        self.icon_emoji = config.get('icon_emoji', ':robot_face:')
    
    async def send(self, message: str, subject: str = "", **kwargs) -> bool:
        """发送Slack通知"""
        if not self.webhook_url:
            logger.warning("Slack通知：没有配置webhook URL")
            return False
        
        try:
            import httpx
            
            # 构建Slack消息
            slack_message = {
                "channel": self.channel,
                "username": self.username,
                "icon_emoji": self.icon_emoji,
                "text": f"*{subject}*\n{message}" if subject else message,
                "attachments": []
            }
            
            # 添加附加信息
            if kwargs.get('color'):
                attachment = {
                    "color": kwargs['color'],
                    "text": message,
                    "ts": datetime.now().timestamp()
                }
                slack_message["attachments"].append(attachment)
            
            # 发送请求
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.webhook_url,
                    json=slack_message,
                    timeout=30
                )
                response.raise_for_status()
            
            logger.info(f"Slack通知发送成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Slack通知发送失败: {e}")
            return False


class DingTalkNotifier(BaseNotifier):
    """钉钉通知器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.webhook_url = config.get('webhook_url', '')
        self.secret = config.get('secret', '')
    
    async def send(self, message: str, subject: str = "", **kwargs) -> bool:
        """发送钉钉通知"""
        if not self.webhook_url:
            logger.warning("钉钉通知：没有配置webhook URL")
            return False
        
        try:
            import httpx
            import hmac
            import hashlib
            import base64
            import urllib.parse
            
            # 构建钉钉消息
            content = f"**{subject}**\n\n{message}" if subject else message
            
            dingtalk_message = {
                "msgtype": "markdown",
                "markdown": {
                    "title": subject or "DataTrans通知",
                    "text": content
                }
            }
            
            # 如果配置了密钥，计算签名
            url = self.webhook_url
            if self.secret:
                timestamp = str(round(datetime.now().timestamp() * 1000))
                secret_enc = self.secret.encode('utf-8')
                string_to_sign = f'{timestamp}\n{self.secret}'
                string_to_sign_enc = string_to_sign.encode('utf-8')
                hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
                sign = urllib.parse.quote_plus(base64.b64encode(hmac_code))
                url = f"{self.webhook_url}&timestamp={timestamp}&sign={sign}"
            
            # 发送请求
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=dingtalk_message,
                    timeout=30
                )
                response.raise_for_status()
                
                result = response.json()
                if result.get('errcode') != 0:
                    raise Exception(f"钉钉API错误: {result.get('errmsg')}")
            
            logger.info(f"钉钉通知发送成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"钉钉通知发送失败: {e}")
            return False


class WebhookNotifier(BaseNotifier):
    """通用Webhook通知器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.webhook_url = config.get('webhook_url', '')
        self.headers = config.get('headers', {})
        self.method = config.get('method', 'POST').upper()
    
    async def send(self, message: str, subject: str = "", **kwargs) -> bool:
        """发送Webhook通知"""
        if not self.webhook_url:
            logger.warning("Webhook通知：没有配置URL")
            return False
        
        try:
            import httpx
            
            # 构建请求数据
            data = {
                "subject": subject,
                "message": message,
                "timestamp": datetime.now().isoformat(),
                "source": "DataTrans",
                **kwargs
            }
            
            # 发送请求
            async with httpx.AsyncClient() as client:
                response = await client.request(
                    method=self.method,
                    url=self.webhook_url,
                    json=data,
                    headers=self.headers,
                    timeout=30
                )
                response.raise_for_status()
            
            logger.info(f"Webhook通知发送成功: {subject}")
            return True
            
        except Exception as e:
            logger.error(f"Webhook通知发送失败: {e}")
            return False


class NotificationManager:
    """通知管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.notifiers: Dict[str, BaseNotifier] = {}
        self._setup_notifiers()
    
    def _setup_notifiers(self):
        """设置通知器"""
        # 日志通知器（默认启用）
        self.notifiers['log'] = LogNotifier(self.config.get('log', {'enabled': True}))
        
        # 邮件通知器
        if 'email' in self.config:
            self.notifiers['email'] = EmailNotifier(self.config['email'])
        
        # Slack通知器
        if 'slack' in self.config:
            self.notifiers['slack'] = SlackNotifier(self.config['slack'])
        
        # 钉钉通知器
        if 'dingtalk' in self.config:
            self.notifiers['dingtalk'] = DingTalkNotifier(self.config['dingtalk'])
        
        # Webhook通知器
        if 'webhook' in self.config:
            self.notifiers['webhook'] = WebhookNotifier(self.config['webhook'])
    
    async def send_notification(
        self,
        message: str,
        subject: str = "",
        channels: Optional[List[str]] = None,
        level: str = "info",
        **kwargs
    ) -> Dict[str, bool]:
        """发送通知到指定渠道"""
        if channels is None:
            channels = list(self.notifiers.keys())
        
        results = {}
        tasks = []
        
        for channel in channels:
            if channel in self.notifiers and self.notifiers[channel].is_enabled():
                task = self._send_to_channel(channel, message, subject, level=level, **kwargs)
                tasks.append((channel, task))
        
        # 并发发送通知
        if tasks:
            task_results = await asyncio.gather(
                *[task for _, task in tasks],
                return_exceptions=True
            )
            
            for i, (channel, _) in enumerate(tasks):
                result = task_results[i]
                if isinstance(result, Exception):
                    logger.error(f"通知发送异常 {channel}: {result}")
                    results[channel] = False
                else:
                    results[channel] = result
        
        return results
    
    async def _send_to_channel(self, channel: str, message: str, subject: str = "", **kwargs) -> bool:
        """发送通知到指定渠道"""
        try:
            notifier = self.notifiers[channel]
            return await notifier.send(message, subject, **kwargs)
        except Exception as e:
            logger.error(f"发送通知到 {channel} 失败: {e}")
            return False
    
    async def send_success_notification(self, dag_id: str, execution_date: str, **kwargs) -> Dict[str, bool]:
        """发送成功通知"""
        subject = f"✅ DAG执行成功: {dag_id}"
        message = f"DAG {dag_id} 在 {execution_date} 执行成功"
        
        return await self.send_notification(
            message=message,
            subject=subject,
            level="info",
            color="good",
            **kwargs
        )
    
    async def send_failure_notification(self, dag_id: str, execution_date: str, error: str, **kwargs) -> Dict[str, bool]:
        """发送失败通知"""
        subject = f"❌ DAG执行失败: {dag_id}"
        message = f"DAG {dag_id} 在 {execution_date} 执行失败\n\n错误信息: {error}"
        
        return await self.send_notification(
            message=message,
            subject=subject,
            level="error",
            color="danger",
            **kwargs
        )
    
    async def send_retry_notification(self, dag_id: str, task_id: str, retry_count: int, **kwargs) -> Dict[str, bool]:
        """发送重试通知"""
        subject = f"🔄 任务重试: {dag_id}.{task_id}"
        message = f"任务 {task_id} 在DAG {dag_id} 中进行第 {retry_count} 次重试"
        
        return await self.send_notification(
            message=message,
            subject=subject,
            level="warning",
            color="warning",
            **kwargs
        )
    
    def add_notifier(self, name: str, notifier: BaseNotifier):
        """添加自定义通知器"""
        self.notifiers[name] = notifier
    
    def remove_notifier(self, name: str):
        """移除通知器"""
        if name in self.notifiers:
            del self.notifiers[name]
    
    def get_notifier_status(self) -> Dict[str, Dict[str, Any]]:
        """获取通知器状态"""
        status = {}
        for name, notifier in self.notifiers.items():
            status[name] = {
                "enabled": notifier.is_enabled(),
                "type": type(notifier).__name__,
                "config": {k: "***" if "password" in k.lower() or "secret" in k.lower() or "token" in k.lower() 
                          else v for k, v in notifier.config.items()}
            }
        return status


# 全局通知管理器实例
_notification_manager: Optional[NotificationManager] = None


def get_notification_manager(config: Optional[Dict[str, Any]] = None) -> NotificationManager:
    """获取通知管理器实例"""
    global _notification_manager
    if _notification_manager is None:
        _notification_manager = NotificationManager(config)
    return _notification_manager


async def send_notification(
    message: str,
    subject: str = "",
    channels: Optional[List[str]] = None,
    **kwargs
) -> Dict[str, bool]:
    """发送通知（便捷函数）"""
    manager = get_notification_manager()
    return await manager.send_notification(message, subject, channels, **kwargs)
