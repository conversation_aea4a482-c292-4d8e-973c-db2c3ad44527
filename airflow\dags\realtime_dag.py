"""
实时处理DAG示例
"""

from datetime import datetime, timedelta
import sys
import os

# 添加项目路径到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(project_root, 'src'))

from data_trans.workflow.dag_templates import create_realtime_dag

# DAG配置
DAG_ID = "realtime_process_example"

# 数据源配置
DATA_SOURCE_CONFIG = {
    "type": "queue",  # 监控Redis队列
    "queue_name": "crawl_tasks",
    "min_messages": 1,
    "max_messages": 100,
    "urls": [
        "https://httpbin.org/json",
        "https://httpbin.org/uuid"
    ]
}

# 传感器配置
SENSOR_CONFIG = {
    "check_interval": 30,  # 每30秒检查一次
    "timeout": 7200  # 2小时超时
}

# 爬虫配置
CRAWLER_CONFIG = {
    "timeout": 15,
    "max_retries": 2,
    "delay": 0.1,
    "realtime_mode": True,
    "user_agent": "DataTrans-Realtime/1.0",
    "headers": {
        "Accept": "application/json"
    }
}

# 清洗配置
CLEANER_CONFIG = {
    "remove_html": True,
    "normalize_whitespace": True,
    "remove_empty": True,
    "extract_json": True,
    "realtime_processing": True,
    "quick_validation": True
}

# 存储配置
STORAGE_CONFIG = {
    "type": "mongodb",
    "collection": "realtime_data",
    "database": "datatrans",
    "realtime_insert": True,
    "create_index": True
}

# 创建DAG
dag = create_realtime_dag(
    dag_id=DAG_ID,
    data_source_config=DATA_SOURCE_CONFIG,
    crawler_config=CRAWLER_CONFIG,
    cleaner_config=CLEANER_CONFIG,
    storage_config=STORAGE_CONFIG,
    sensor_config=SENSOR_CONFIG,
    catchup=False,
    tags=["example", "realtime", "queue"]
)

# 导出DAG供Airflow使用
globals()[DAG_ID] = dag
