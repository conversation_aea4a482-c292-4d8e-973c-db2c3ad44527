"""
配置管理工具类 - 统一管理连接和变量
"""

import logging
from typing import Any, Dict, Optional, List
from .connections import ConnectionManager, Connection, get_connection_manager
from .variables import VariableManager, Variable, get_variable_manager

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器 - 统一管理连接和变量"""
    
    def __init__(self):
        self.connection_manager = get_connection_manager()
        self.variable_manager = get_variable_manager()
    
    # 连接管理方法
    def add_connection(
        self,
        conn_id: str,
        conn_type: str,
        host: Optional[str] = None,
        port: Optional[int] = None,
        schema: Optional[str] = None,
        login: Optional[str] = None,
        password: Optional[str] = None,
        extra: Optional[Dict[str, Any]] = None,
        description: Optional[str] = None
    ) -> bool:
        """添加连接"""
        connection = Connection(
            conn_id=conn_id,
            conn_type=conn_type,
            host=host,
            port=port,
            schema=schema,
            login=login,
            password=password,
            extra=extra,
            description=description
        )
        return self.connection_manager.add_connection(connection)
    
    def get_connection(self, conn_id: str) -> Optional[Connection]:
        """获取连接"""
        return self.connection_manager.get_connection(conn_id)
    
    def get_connection_uri(self, conn_id: str) -> Optional[str]:
        """获取连接URI"""
        conn = self.get_connection(conn_id)
        return conn.get_uri() if conn else None
    
    def test_connection(self, conn_id: str) -> bool:
        """测试连接"""
        return self.connection_manager.test_connection(conn_id)
    
    def list_connections(self) -> List[Connection]:
        """列出所有连接"""
        return self.connection_manager.list_connections()
    
    def update_connection(self, conn_id: str, **kwargs) -> bool:
        """更新连接"""
        return self.connection_manager.update_connection(conn_id, **kwargs)
    
    def delete_connection(self, conn_id: str) -> bool:
        """删除连接"""
        return self.connection_manager.delete_connection(conn_id)
    
    # 变量管理方法
    def set_variable(self, key: str, value: Any, description: Optional[str] = None, is_encrypted: bool = False) -> bool:
        """设置变量"""
        return self.variable_manager.set_variable(key, value, description, is_encrypted)
    
    def get_variable(self, key: str, default_value: Any = None) -> Any:
        """获取变量"""
        return self.variable_manager.get_variable(key, default_value)
    
    def get_variable_info(self, key: str) -> Optional[Variable]:
        """获取变量信息"""
        return self.variable_manager.get_variable_info(key)
    
    def list_variables(self) -> List[Variable]:
        """列出所有变量"""
        return self.variable_manager.list_variables()
    
    def update_variable(self, key: str, **kwargs) -> bool:
        """更新变量"""
        return self.variable_manager.update_variable(key, **kwargs)
    
    def delete_variable(self, key: str) -> bool:
        """删除变量"""
        return self.variable_manager.delete_variable(key)
    
    # 配置模板方法
    def setup_crawler_config(
        self,
        config_name: str = "default_crawler_config",
        timeout: int = 30,
        max_retries: int = 3,
        delay: float = 1.0,
        user_agent: str = "DataTrans/1.0",
        headers: Optional[Dict[str, str]] = None
    ) -> bool:
        """设置爬虫配置模板"""
        config = {
            "timeout": timeout,
            "max_retries": max_retries,
            "delay": delay,
            "user_agent": user_agent,
            "headers": headers or {}
        }
        return self.set_variable(config_name, config, "爬虫配置模板")
    
    def setup_storage_config(
        self,
        config_name: str = "default_storage_config",
        storage_type: str = "mongodb",
        database: str = "datatrans",
        collection: str = "crawl_data",
        connection_id: str = "mongodb_default"
    ) -> bool:
        """设置存储配置模板"""
        config = {
            "type": storage_type,
            "database": database,
            "collection": collection,
            "connection_id": connection_id
        }
        return self.set_variable(config_name, config, "存储配置模板")
    
    def setup_notification_config(
        self,
        config_name: str = "default_notification_config",
        channels: List[str] = None,
        email_recipients: List[str] = None,
        slack_webhook: str = "",
        dingtalk_webhook: str = ""
    ) -> bool:
        """设置通知配置模板"""
        config = {
            "channels": channels or ["log"],
            "email_recipients": email_recipients or [],
            "slack_webhook": slack_webhook,
            "dingtalk_webhook": dingtalk_webhook
        }
        return self.set_variable(config_name, config, "通知配置模板")
    
    # 批量操作方法
    def setup_default_configs(self) -> bool:
        """设置默认配置"""
        try:
            # 设置默认连接
            self.add_connection(
                conn_id="postgres_default",
                conn_type="postgres",
                host="localhost",
                port=5432,
                schema="datatrans",
                login="postgres",
                password="postgres",
                description="默认PostgreSQL连接"
            )
            
            self.add_connection(
                conn_id="redis_default",
                conn_type="redis",
                host="localhost",
                port=6379,
                schema="0",
                description="默认Redis连接"
            )
            
            self.add_connection(
                conn_id="mongodb_default",
                conn_type="mongodb",
                host="localhost",
                port=27017,
                schema="datatrans",
                description="默认MongoDB连接"
            )
            
            # 设置默认变量
            self.setup_crawler_config()
            self.setup_storage_config()
            self.setup_notification_config()
            
            logger.info("默认配置设置完成")
            return True
        except Exception as e:
            logger.error(f"设置默认配置失败: {e}")
            return False
    
    def validate_configs(self) -> Dict[str, bool]:
        """验证所有配置"""
        results = {
            "connections": {},
            "variables": {}
        }
        
        # 验证连接
        for conn in self.list_connections():
            results["connections"][conn.conn_id] = self.test_connection(conn.conn_id)
        
        # 验证关键变量
        key_variables = ["crawler_config", "storage_config", "notification_config"]
        for key in key_variables:
            var = self.get_variable_info(key)
            results["variables"][key] = var is not None
        
        return results
    
    def export_all_configs(self, connections_file: str, variables_file: str) -> bool:
        """导出所有配置"""
        try:
            # 导出连接（简化实现，实际应该有专门的导出方法）
            connections_data = [conn.to_dict() for conn in self.list_connections()]
            import json
            with open(connections_file, 'w', encoding='utf-8') as f:
                json.dump({"connections": connections_data}, f, indent=2, ensure_ascii=False)
            
            # 导出变量
            self.variable_manager.export_variables(variables_file)
            
            logger.info(f"配置导出完成: {connections_file}, {variables_file}")
            return True
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        connections = self.list_connections()
        variables = self.list_variables()
        
        return {
            "connections": {
                "total": len(connections),
                "by_type": {conn.conn_type: sum(1 for c in connections if c.conn_type == conn.conn_type) 
                           for conn in connections}
            },
            "variables": {
                "total": len(variables),
                "encrypted": sum(1 for var in variables if var.is_encrypted)
            },
            "last_updated": max(
                [conn.updated_at for conn in connections] + 
                [var.updated_at for var in variables],
                default="未知"
            )
        }


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """获取配置管理器实例"""
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager
