#!/usr/bin/env python3
"""
TaskExecutor命令行工具

提供命令行接口来执行数据采集任务。
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Optional

import click
import structlog

from .task_executor import TaskExecutor, TaskExecutorConfig


# 配置日志
def setup_logging(level: str = "INFO") -> None:
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
        ],
    )

    # 配置结构化日志
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.JSONRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


@click.group()
@click.option(
    "--log-level",
    default="INFO",
    type=click.Choice(["DEBUG", "INFO", "WARNING", "ERROR"]),
    help="日志级别",
)
@click.pass_context
def cli(ctx: click.Context, log_level: str) -> None:
    """数据采集任务执行器命令行工具"""
    setup_logging(log_level)
    ctx.ensure_object(dict)
    ctx.obj["log_level"] = log_level


@cli.command()
@click.option(
    "--config",
    "-c",
    required=True,
    type=click.Path(exists=True),
    help="任务配置文件路径",
)
@click.option("--timeout", "-t", default=300.0, type=float, help="任务超时时间(秒)")
@click.option("--max-concurrent", "-m", default=10, type=int, help="最大并发任务数")
@click.pass_context
async def run(
    ctx: click.Context, config: str, timeout: float, max_concurrent: int
) -> None:
    """从配置文件运行单个任务"""
    logger = structlog.get_logger(__name__)

    try:
        # 创建执行器配置
        executor_config = TaskExecutorConfig(
            max_concurrent_tasks=max_concurrent,
            timeout=timeout,
            log_level=ctx.obj["log_level"],
        )

        # 创建执行器
        executor = TaskExecutor(executor_config)

        logger.info("开始执行任务", config_file=config)

        # 从文件执行任务
        result = await executor.execute_from_file(Path(config))

        # 输出结果
        click.echo("\n任务执行完成:")
        click.echo(f"  任务ID: {result.task_id}")
        click.echo(f"  状态: {result.status.value}")
        click.echo(f"  爬取数据: {result.crawled_count} 条")
        click.echo(f"  清洗数据: {result.cleaned_count} 条")
        click.echo(f"  存储数据: {result.stored_count} 条")

        if result.duration:
            click.echo(f"  执行时长: {result.duration:.2f} 秒")

        if result.error_message:
            click.echo(f"  错误信息: {result.error_message}", err=True)

        # 显示最近的日志
        if result.logs:
            click.echo("\n最近的执行日志:")
            for log_entry in result.logs[-10:]:
                click.echo(f"  {log_entry}")

        await executor.cleanup()

        # 根据执行结果设置退出码
        if result.status.value == "success":
            sys.exit(0)
        else:
            sys.exit(1)

    except Exception as e:
        logger.error("任务执行失败", error=str(e))
        click.echo(f"任务执行失败: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option(
    "--config-dir",
    "-d",
    required=True,
    type=click.Path(exists=True),
    help="配置文件目录",
)
@click.option("--pattern", "-p", default="*.json", help="配置文件匹配模式")
@click.option("--max-concurrent", "-m", default=5, type=int, help="最大并发任务数")
@click.option("--timeout", "-t", default=300.0, type=float, help="单个任务超时时间(秒)")
@click.pass_context
async def batch(
    ctx: click.Context,
    config_dir: str,
    pattern: str,
    max_concurrent: int,
    timeout: float,
) -> None:
    """批量执行目录中的任务配置文件"""
    logger = structlog.get_logger(__name__)

    try:
        config_path = Path(config_dir)
        config_files = list(config_path.glob(pattern))

        if not config_files:
            click.echo(f"在目录 {config_dir} 中未找到匹配 {pattern} 的配置文件")
            sys.exit(1)

        click.echo(f"找到 {len(config_files)} 个配置文件")

        # 创建执行器
        executor_config = TaskExecutorConfig(
            max_concurrent_tasks=max_concurrent,
            timeout=timeout,
            log_level=ctx.obj["log_level"],
        )
        executor = TaskExecutor(executor_config)

        # 加载所有任务配置
        tasks = []
        for config_file in config_files:
            try:
                task_config = await executor.load_task_from_file(config_file)
                tasks.append(task_config.model_dump())
                logger.info(
                    "加载任务配置", file=str(config_file), task_id=task_config.task_id
                )
            except Exception as e:
                logger.error("配置文件加载失败", file=str(config_file), error=str(e))
                continue

        if not tasks:
            click.echo("没有有效的任务配置")
            sys.exit(1)

        click.echo(f"开始批量执行 {len(tasks)} 个任务...")

        # 批量执行任务
        results = await executor.execute_batch(tasks)

        # 统计结果
        success_count = 0
        failed_count = 0
        total_crawled = 0
        total_cleaned = 0
        total_stored = 0

        click.echo("\n批量执行结果:")
        for result in results:
            status_symbol = "✓" if result.status.value == "success" else "✗"
            click.echo(f"  {status_symbol} {result.task_id}: {result.status.value}")

            if result.status.value == "success":
                success_count += 1
            else:
                failed_count += 1
                if result.error_message:
                    click.echo(f"    错误: {result.error_message}")

            total_crawled += result.crawled_count
            total_cleaned += result.cleaned_count
            total_stored += result.stored_count

        # 输出统计信息
        click.echo("\n执行统计:")
        click.echo(f"  总任务数: {len(results)}")
        click.echo(f"  成功: {success_count}")
        click.echo(f"  失败: {failed_count}")
        click.echo(f"  成功率: {success_count/len(results)*100:.1f}%")
        click.echo(f"  总爬取数据: {total_crawled} 条")
        click.echo(f"  总清洗数据: {total_cleaned} 条")
        click.echo(f"  总存储数据: {total_stored} 条")

        await executor.cleanup()

        # 根据成功率设置退出码
        if success_count == len(results):
            sys.exit(0)
        elif success_count > 0:
            sys.exit(2)  # 部分成功
        else:
            sys.exit(1)  # 全部失败

    except Exception as e:
        logger.error("批量执行失败", error=str(e))
        click.echo(f"批量执行失败: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option("--output", "-o", default="task_template.json", help="输出文件路径")
@click.option(
    "--crawler-type", default="web", type=click.Choice(["web", "api"]), help="爬虫类型"
)
@click.option(
    "--storage-type",
    default="mongodb",
    type=click.Choice(["mongodb", "redis"]),
    help="存储类型",
)
def template(output: str, crawler_type: str, storage_type: str) -> None:
    """生成任务配置模板文件"""
    template_config = {
        "task_id": "example_task_001",
        "task_type": "crawl_clean_store",
        "crawler_type": crawler_type,
        "crawler_config": {
            "timeout": 30.0,
            "max_retries": 3,
            "rate_limit_per_second": 1.0,
        },
        "urls": ["https://example.com/api/data", "https://example.com/page1"],
        "cleaner_type": "text",
        "cleaner_config": {
            "strict_mode": False,
            "skip_invalid": True,
            "preserve_original": True,
        },
        "cleaning_rules": [
            {
                "name": "normalize_whitespace",
                "field": "content",
                "rule_type": "regex",
                "pattern": "\\s+",
                "replacement": " ",
                "priority": 1,
            }
        ],
        "storage_type": storage_type,
        "storage_config": {},
        "raw_collection": "raw_data",
        "cleaned_collection": "cleaned_data",
        "max_retries": 3,
        "retry_delay": 1.0,
        "timeout": 300.0,
    }

    output_path = Path(output)
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(template_config, f, indent=2, ensure_ascii=False)

    click.echo(f"任务配置模板已生成: {output_path}")
    click.echo("请根据需要修改配置参数")


def main() -> None:
    """主入口函数"""

    # 将异步命令包装为同步
    def async_command(f):
        def wrapper(*args, **kwargs):
            return asyncio.run(f(*args, **kwargs))

        return wrapper

    # 包装异步命令
    cli.commands["run"].callback = async_command(cli.commands["run"].callback)
    cli.commands["batch"].callback = async_command(cli.commands["batch"].callback)

    cli()


if __name__ == "__main__":
    main()
